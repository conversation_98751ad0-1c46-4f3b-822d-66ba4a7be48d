#!/usr/bin/env python3
"""
Test script for the improved OpenAI API rate limiting and batch processing system.

This script tests the enhanced ChatGPT API integration with:
- Rate limiting and request queuing
- Batch processing for multiple documents
- Enhanced retry logic for 429 errors
- Performance monitoring

Run this before executing main.py to ensure the system works correctly.
"""

import os
import sys
import time
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.interpreter.chatgpt_api import (
    analyze_mail_and_pdf, 
    analyze_mail_and_batch_pdfs,
    BatchRequestManager,
    _batch_manager
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

# Test data
SAMPLE_EMAIL_BODY = """
Dear Team,

Please find attached the safety data sheets for the chemicals we received today.
These need to be reviewed and filed according to our safety protocols.

Contact <NAME_EMAIL> if you have any questions.
Emergency contact: +1-800-555-0123

Best regards,
Safety Officer
"""

SAMPLE_PDF_TEXTS = [
    {
        "filename": "sodium_chloride_sds.pdf",
        "text": """
SAFETY DATA SHEET
Product Name: Sodium Chloride for Analysis
Product Number: 106404
CAS-No.: 7647-14-5
Company: EMD Millipore Corporation
Revision Date: 06/20/2018

HAZARD IDENTIFICATION
GHS Classification: Not a dangerous substance according to GHS
Signal Word: None
Hazard Statements: None

COMPOSITION/INFORMATION ON INGREDIENTS
Chemical Name: Sodium chloride
CAS Number: 7647-14-5
Concentration: >99%

FIRST AID MEASURES
Eye Contact: Rinse with water
Skin Contact: Wash with soap and water
Inhalation: Move to fresh air
Ingestion: Rinse mouth with water

EMERGENCY CONTACT
USA: ************ CHEMTREC
International: +1-************ CHEMTREC
        """
    },
    {
        "filename": "hydrochloric_acid_sds.pdf", 
        "text": """
SAFETY DATA SHEET
Product Name: Hydrochloric Acid
Company: Fisher Scientific Company
Creation Date: 24-Aug-2009
Revision Date: 13-Oct-2023

HAZARD IDENTIFICATION
GHS Classification: 
- Corrosive to metals (Category 1)
- Skin Corrosion/Irritation (Category 1)
- Serious Eye Damage (Category 1)
- STOT SE (Category 3)

Signal Word: DANGER
Hazard Statements:
- May be corrosive to metals
- Causes severe skin burns and eye damage
- May cause respiratory irritation

EMERGENCY CONTACT
CHEMTREC Inside USA: ************
CHEMTREC Outside USA: 001-************
        """
    },
    {
        "filename": "ethanol_sds.pdf",
        "text": """
SAFETY DATA SHEET
Product Name: Ethanol Solution 96%
Company: Fisher Scientific Company
Creation Date: 21-May-2009
Revision Date: 07-Feb-2025

HAZARD IDENTIFICATION
GHS Classification:
- Flammable liquids (Category 2)
- Serious Eye Damage/Eye Irritation (Category 2)

Signal Word: DANGER
Hazard Statements:
- Highly flammable liquid and vapor
- Causes serious eye irritation

NFPA Ratings: Health 2, Flammability 3, Instability 0

EMERGENCY CONTACT
CHEMTREC Inside USA: ************
CHEMTREC Outside USA: 001-************
        """
    },
    {
        "filename": "acetone_sds.pdf",
        "text": """
SAFETY DATA SHEET
Product Name: Acetone
CAS No: 67-64-1
Company: Fisher Scientific Company
Creation Date: 28-Apr-2009
Revision Date: 13-Oct-2023

HAZARD IDENTIFICATION
Classified as hazardous under 2012 OSHA Hazard Communication Standard

GHS Classification:
- Flammable liquids (Category 2)
- Serious Eye Damage/Eye Irritation (Category 2)
- STOT SE (Category 3)
- STOT RE (Category 2)

Signal Word: DANGER
Hazard Statements:
- Highly flammable liquid and vapor
- Causes serious eye irritation
- May cause drowsiness or dizziness
- May cause damage to organs through prolonged exposure
        """
    }
]


def test_single_document_processing():
    """Test processing a single document with rate limiting."""
    log.info("🧪 Testing single document processing...")
    
    start_time = time.time()
    result = analyze_mail_and_pdf(
        mail_body=SAMPLE_EMAIL_BODY,
        pdf_text=SAMPLE_PDF_TEXTS[0]["text"],
        language="English",
        use_batch_manager=True
    )
    processing_time = time.time() - start_time
    
    log.info(f"✅ Single document processed in {processing_time:.2f}s")
    log.info(f"📄 Document type: {result.get('doc_type', 'Unknown')}")
    log.info(f"📝 Summary length: {len(result.get('summary', ''))}")
    log.info(f"🔑 Extracted fields: {len(result.get('extracted_fields', {}))}")
    
    return result


def test_batch_processing():
    """Test batch processing multiple documents."""
    log.info("🧪 Testing batch processing...")
    
    start_time = time.time()
    result = analyze_mail_and_batch_pdfs(
        mail_body=SAMPLE_EMAIL_BODY,
        document_texts=SAMPLE_PDF_TEXTS,
        language="English",
        use_batch_manager=True
    )
    processing_time = time.time() - start_time
    
    log.info(f"✅ Batch processed {len(SAMPLE_PDF_TEXTS)} documents in {processing_time:.2f}s")
    log.info(f"📄 Document type: {result.get('doc_type', 'Unknown')}")
    log.info(f"📝 Summary length: {len(result.get('summary', ''))}")
    log.info(f"🔑 Extracted fields: {len(result.get('extracted_fields', {}))}")
    
    return result


def test_rate_limiting():
    """Test rate limiting by making multiple rapid requests."""
    log.info("🧪 Testing rate limiting with multiple rapid requests...")
    
    start_time = time.time()
    results = []
    
    for i in range(3):
        log.info(f"📤 Sending request {i+1}/3...")
        result = analyze_mail_and_pdf(
            mail_body=SAMPLE_EMAIL_BODY,
            pdf_text=SAMPLE_PDF_TEXTS[i]["text"],
            language="English",
            use_batch_manager=True
        )
        results.append(result)
        log.info(f"✅ Request {i+1} completed")
    
    total_time = time.time() - start_time
    log.info(f"✅ All 3 requests completed in {total_time:.2f}s")
    log.info(f"⏱️ Average time per request: {total_time/3:.2f}s")
    
    return results


def test_fallback_processing():
    """Test fallback to direct processing when batch manager is disabled."""
    log.info("🧪 Testing fallback processing (batch manager disabled)...")
    
    start_time = time.time()
    result = analyze_mail_and_pdf(
        mail_body=SAMPLE_EMAIL_BODY,
        pdf_text=SAMPLE_PDF_TEXTS[0]["text"],
        language="English",
        use_batch_manager=False  # Disable batch manager
    )
    processing_time = time.time() - start_time
    
    log.info(f"✅ Fallback processing completed in {processing_time:.2f}s")
    log.info(f"📄 Document type: {result.get('doc_type', 'Unknown')}")
    
    return result


def print_test_results(test_name: str, result: Dict[str, Any]):
    """Print formatted test results."""
    print(f"\n{'='*60}")
    print(f"📊 {test_name} Results")
    print(f"{'='*60}")
    print(f"📄 Document Type: {result.get('doc_type', 'Unknown')}")
    print(f"📝 Summary Preview: {result.get('summary', '')[:200]}...")
    print(f"🔑 Extracted Fields: {json.dumps(result.get('extracted_fields', {}), indent=2)}")


def main():
    """Run all tests."""
    print("🚀 Starting OpenAI API Rate Limiting Tests")
    print("=" * 60)
    
    # Check if API key is set
    if not os.getenv("OPENAI_API_KEY"):
        log.error("❌ OPENAI_API_KEY environment variable not set!")
        sys.exit(1)
    
    log.info("✅ OpenAI API key found")
    
    try:
        # Test 1: Single document processing
        result1 = test_single_document_processing()
        print_test_results("Single Document Processing", result1)
        
        # Test 2: Batch processing
        result2 = test_batch_processing()
        print_test_results("Batch Processing", result2)
        
        # Test 3: Rate limiting
        results3 = test_rate_limiting()
        print_test_results("Rate Limiting Test (Last Result)", results3[-1])
        
        # Test 4: Fallback processing
        result4 = test_fallback_processing()
        print_test_results("Fallback Processing", result4)
        
        print(f"\n{'='*60}")
        print("🎉 All tests completed successfully!")
        print("✅ The rate limiting system is ready for production use.")
        print("💡 You can now run main.py with confidence.")
        print(f"{'='*60}")
        
    except Exception as e:
        log.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
